import pandas as pd
import openpyxl
from openpyxl import load_workbook
import os
import glob

def get_excel_files():
    """Get all Excel files except the template"""
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls")
    # Exclude the template file
    template_file = "SARITA DAGAR CBE REPORT.xlsx"
    excel_files = [f for f in all_files if f != template_file and not f.startswith('~')]
    return sorted(excel_files)

def analyze_file_structure(filename):
    """Analyze structure of a single Excel file"""
    print(f"\n{'='*60}")
    print(f"Analyzing: {filename}")
    print(f"{'='*60}")
    
    try:
        # Try to load with openpyxl first
        wb = load_workbook(filename, data_only=True)
        print(f"Sheets: {wb.sheetnames}")
        
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            print(f"\n--- Sheet: {sheet_name} ---")
            print(f"Dimensions: {ws.max_row} rows x {ws.max_column} columns")
            
            # Get some sample data
            sample_data = []
            for row in range(1, min(6, ws.max_row + 1)):
                row_data = []
                for col in range(1, min(ws.max_column + 1, 10)):
                    cell = ws.cell(row=row, column=col)
                    value = cell.value if cell.value is not None else ""
                    row_data.append(str(value)[:30])
                sample_data.append(row_data)
            
            print("Sample data (first 5 rows, up to 10 columns):")
            for i, row in enumerate(sample_data, 1):
                print(f"Row {i}: {row}")
        
        wb.close()
        
        # Also try pandas
        try:
            df_dict = pd.read_excel(filename, sheet_name=None)
            print(f"\nPandas analysis:")
            for sheet_name, df in df_dict.items():
                print(f"Sheet '{sheet_name}': {df.shape[0]} rows, {df.shape[1]} columns")
                if not df.empty:
                    print(f"Columns: {list(df.columns)[:5]}...")  # First 5 columns
        except Exception as e:
            print(f"Pandas read error: {e}")
            
    except Exception as e:
        print(f"Error reading {filename}: {e}")
        # Try with pandas only
        try:
            df_dict = pd.read_excel(filename, sheet_name=None)
            print(f"Pandas-only analysis:")
            for sheet_name, df in df_dict.items():
                print(f"Sheet '{sheet_name}': {df.shape[0]} rows, {df.shape[1]} columns")
        except Exception as e2:
            print(f"Complete failure to read {filename}: {e2}")

def main():
    excel_files = get_excel_files()
    print(f"Found {len(excel_files)} Excel files to analyze:")
    for i, f in enumerate(excel_files, 1):
        print(f"{i:2d}. {f}")
    
    # Analyze each file
    for filename in excel_files:
        analyze_file_structure(filename)

if __name__ == "__main__":
    main()
