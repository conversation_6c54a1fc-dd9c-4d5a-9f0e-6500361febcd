import pandas as pd
import openpyxl
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
import os
import glob
import warnings
from datetime import datetime
import re
from PIL import Image as PILImage
import io
import tempfile

warnings.filterwarnings('ignore')

def get_source_excel_files():
    """Get all original Excel files, excluding template and generated files"""
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls")
    
    # Files to exclude
    exclude_files = [
        "SARITA DAGAR CBE REPORT.xlsx",  # Template file
    ]
    
    # Patterns to exclude
    exclude_patterns = [
        'consolidated_cbe_report',
        'professional_cbe_report',
        'comprehensive_cbe_report',
        'final_cbe_report',
        'untitled spreadsheet1',
        'untitled spreadsheet2', 
        'copy of book1',
        '~'
    ]
    
    source_files = []
    
    for file in sorted(all_files):
        file_lower = file.lower()
        
        if file in exclude_files:
            continue
            
        should_skip = False
        for pattern in exclude_patterns:
            if pattern in file_lower:
                print(f"Skipping: {file}")
                should_skip = True
                break
        
        if not should_skip:
            source_files.append(file)
    
    return source_files

def extract_all_data_from_file(filename):
    """Extract ALL data from a file including headers, sections, and structure"""
    print(f"Processing: {filename}")
    
    file_data = {
        'filename': filename,
        'sheets': {},
        'images': [],
        'total_rows': 0,
        'total_sections': 0
    }
    
    try:
        # First, try to extract images
        try:
            wb_images = load_workbook(filename)
            for sheet_name in wb_images.sheetnames:
                ws = wb_images[sheet_name]
                sheet_images = []
                if hasattr(ws, '_images'):
                    for img in ws._images:
                        try:
                            sheet_images.append({
                                'anchor': str(img.anchor),
                                'sheet': sheet_name,
                                'width': getattr(img, 'width', 100),
                                'height': getattr(img, 'height', 100)
                            })
                        except:
                            pass
                file_data['images'].extend(sheet_images)
            wb_images.close()
            print(f"  Found {len(file_data['images'])} images")
        except Exception as e:
            print(f"  Could not extract images: {e}")
        
        # Read all sheets with pandas
        try:
            sheets_dict = pd.read_excel(filename, sheet_name=None, header=None)
        except:
            try:
                # Try with xlrd for .xls files
                sheets_dict = pd.read_excel(filename, sheet_name=None, header=None, engine='xlrd')
            except:
                sheets_dict = {0: pd.read_excel(filename, sheet_name=0, header=None)}
        
        for sheet_name, df in sheets_dict.items():
            if df.empty:
                continue
                
            print(f"  Processing sheet: {sheet_name} ({df.shape[0]} rows, {df.shape[1]} cols)")
            
            # Extract all sections from this sheet
            sheet_sections = extract_sections_from_sheet(df, filename, sheet_name)
            file_data['sheets'][sheet_name] = sheet_sections
            file_data['total_rows'] += df.shape[0]
            file_data['total_sections'] += len(sheet_sections)
        
        print(f"  Extracted {file_data['total_sections']} sections from {len(file_data['sheets'])} sheets")
        return file_data
        
    except Exception as e:
        print(f"  Error processing {filename}: {e}")
        return file_data

def extract_sections_from_sheet(df, filename, sheet_name):
    """Extract all meaningful sections from a sheet"""
    sections = []
    current_section = None
    
    # Convert all data to string for processing
    df_str = df.astype(str).fillna('')
    
    for idx, row in df_str.iterrows():
        row_data = [cell.strip() for cell in row if cell.strip() and cell.strip().lower() != 'nan']
        
        if not row_data:  # Empty row
            if current_section and current_section['data']:
                sections.append(current_section)
                current_section = None
            continue
        
        row_text = ' '.join(row_data).lower()
        
        # Detect section headers/titles
        if detect_section_header(row_text, row_data):
            if current_section and current_section['data']:
                sections.append(current_section)
            
            current_section = {
                'type': 'header',
                'title': ' '.join(row_data),
                'start_row': idx,
                'data': [],
                'filename': filename,
                'sheet': sheet_name
            }
        
        # Detect data table headers
        elif detect_table_header(row_text, row_data):
            if current_section and current_section['data']:
                sections.append(current_section)
            
            current_section = {
                'type': 'data_table',
                'title': 'Data Table',
                'headers': row_data,
                'start_row': idx,
                'data': [],
                'filename': filename,
                'sheet': sheet_name
            }
        
        # Add data to current section
        else:
            if not current_section:
                current_section = {
                    'type': 'data',
                    'title': 'Data Section',
                    'start_row': idx,
                    'data': [],
                    'filename': filename,
                    'sheet': sheet_name
                }
            
            # Process the row data
            processed_row = process_data_row(row_data, filename)
            if processed_row:
                current_section['data'].append(processed_row)
    
    # Add the last section
    if current_section and current_section['data']:
        sections.append(current_section)
    
    return sections

def detect_section_header(row_text, row_data):
    """Detect if a row is a section header"""
    header_indicators = [
        'name of district', 'name of project', 'month & year',
        'district:', 'project:', 'activity held:',
        'cbe report', 'monthly activity'
    ]
    
    return any(indicator in row_text for indicator in header_indicators)

def detect_table_header(row_text, row_data):
    """Detect if a row contains table headers"""
    header_keywords = ['sno', 's.no', 'awc', 'aww', 'date', 'event', 'participant', 'phone', 'remark']
    
    # Count how many header keywords are present
    keyword_count = sum(1 for keyword in header_keywords if keyword in row_text)
    
    return keyword_count >= 3

def process_data_row(row_data, filename):
    """Process a data row and extract structured information"""
    if len(row_data) < 2:
        return None
    
    processed = {
        'source_file': filename,
        'raw_data': row_data,
        'sno': '',
        'awc_no': '',
        'aww_name': '',
        'date': '',
        'event_name': '',
        'participant_name': '',
        'phone': '',
        'remarks': ''
    }
    
    for i, cell in enumerate(row_data):
        if not cell:
            continue
        
        # Serial number (usually first column, numeric)
        if i == 0 and cell.isdigit() and len(cell) <= 3:
            processed['sno'] = cell
        
        # AWC number
        elif cell.isdigit() and 1 <= len(cell) <= 3 and not processed['awc_no']:
            processed['awc_no'] = cell
        
        # Date detection
        elif re.search(r'\d{4}|\d{1,2}[./]\d{1,2}', cell):
            processed['date'] = standardize_date(cell)
        
        # Phone number detection
        elif re.search(r'[6-9]\d{9}', cell):
            name, phone = extract_participant_data(cell)
            if name:
                processed['participant_name'] = name
            if phone:
                processed['phone'] = phone
        
        # Event names
        elif any(event in cell.lower() for event in ['handwash', 'godbharai', 'annaprashan', 'pre-school', 'breast feeding']):
            processed['event_name'] = cell
        
        # Names (alphabetic, reasonable length)
        elif re.match(r'^[a-zA-Z\s]+$', cell) and 3 <= len(cell) <= 30:
            if not processed['aww_name'] and i < 5:
                processed['aww_name'] = cell
            elif not processed['participant_name']:
                processed['participant_name'] = cell
    
    return processed

def extract_participant_data(text):
    """Extract participant name and phone from combined text"""
    if not text or pd.isna(text):
        return "", ""
    
    text = str(text).strip()
    
    # Extract phone number
    phone_match = re.search(r'[6-9]\d{9}', text)
    phone = phone_match.group() if phone_match else ""
    
    # Extract name
    name = text
    if phone:
        name = re.sub(r'[6-9]\d{9}', '', name)
    
    name = re.sub(r'[-=,\s]+', ' ', name).strip()
    name = re.sub(r'^[^a-zA-Z]*', '', name)
    name = re.sub(r'[^a-zA-Z\s]*$', '', name)
    
    return name.strip(), phone

def standardize_date(date_val):
    """Convert various date formats to standard YYYY-MM-DD"""
    if pd.isna(date_val) or not date_val:
        return ""
    
    if isinstance(date_val, datetime):
        return date_val.strftime('%Y-%m-%d')
    
    date_str = str(date_val).strip()
    
    patterns = [
        (r'(\d{4})-(\d{1,2})-(\d{1,2})', lambda m: f"{m.group(1)}-{m.group(2):0>2}-{m.group(3):0>2}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{4})', lambda m: f"{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
        (r'(\d{1,2})/(\d{1,2})/(\d{4})', lambda m: f"{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{2})', lambda m: f"20{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
    ]
    
    for pattern, formatter in patterns:
        match = re.search(pattern, date_str)
        if match:
            try:
                return formatter(match)
            except:
                continue
    
    return date_str[:10] if len(date_str) >= 10 else date_str

def create_comprehensive_excel(all_file_data):
    """Create comprehensive Excel file with all sections from all files"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Comprehensive CBE Report"
    
    # Professional styling
    header_font = Font(name='Calibri', size=14, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='2F5597', end_color='2F5597', fill_type='solid')
    
    section_font = Font(name='Calibri', size=12, bold=True, color='000000')
    section_fill = PatternFill(start_color='E8F1FF', end_color='E8F1FF', fill_type='solid')
    
    data_font = Font(name='Calibri', size=11)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    # Main headers
    main_headers = [
        'S.No.', 'Source File', 'Sheet Name', 'AWC No.', 'AWW Name', 
        'Date of Event', 'Event Name', 'Participant Name', 'Phone Number', 
        'Raw Data', 'Section Type', 'Remarks'
    ]
    
    # Set main headers
    for col, header in enumerate(main_headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        cell.border = border
    
    # Set column widths
    column_widths = [8, 25, 15, 10, 20, 15, 25, 20, 15, 30, 15, 20]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(col)].width = width
    
    current_row = 2
    record_number = 1
    
    # Process all file data
    for file_data in all_file_data:
        filename = file_data['filename']
        
        # Add file separator
        file_header_cell = ws.cell(row=current_row, column=1, value=f"=== FILE: {filename} ===")
        file_header_cell.font = Font(name='Calibri', size=12, bold=True, color='FFFFFF')
        file_header_cell.fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        
        # Merge cells for file header
        ws.merge_cells(f'A{current_row}:L{current_row}')
        current_row += 1
        
        # Process each sheet
        for sheet_name, sections in file_data['sheets'].items():
            for section in sections:
                # Add section header
                section_cell = ws.cell(row=current_row, column=1, value=f"Section: {section['title']}")
                section_cell.font = section_font
                section_cell.fill = section_fill
                ws.merge_cells(f'A{current_row}:L{current_row}')
                current_row += 1
                
                # Add section data
                for data_row in section['data']:
                    row_data = [
                        record_number,
                        filename,
                        sheet_name,
                        data_row.get('awc_no', ''),
                        data_row.get('aww_name', ''),
                        data_row.get('date', ''),
                        data_row.get('event_name', ''),
                        data_row.get('participant_name', ''),
                        data_row.get('phone', ''),
                        ' | '.join(data_row.get('raw_data', [])),
                        section['type'],
                        data_row.get('remarks', '')
                    ]
                    
                    for col, value in enumerate(row_data, 1):
                        cell = ws.cell(row=current_row, column=col, value=value)
                        cell.font = data_font
                        cell.alignment = data_alignment
                        cell.border = border
                    
                    current_row += 1
                    record_number += 1
        
        current_row += 1  # Add space between files
    
    # Set row heights
    for row in range(1, current_row):
        ws.row_dimensions[row].height = 25
    
    return wb

def main():
    print("=== COMPREHENSIVE CBE REPORT CONSOLIDATION ===")
    print("Including ALL sections from ALL files...\n")
    
    source_files = get_source_excel_files()
    print(f"Processing {len(source_files)} source files:\n")
    
    for i, filename in enumerate(source_files, 1):
        print(f"  {i:2d}. {filename}")
    
    print("\n" + "="*80)
    
    all_file_data = []
    total_sections = 0
    total_records = 0
    
    for filename in source_files:
        file_data = extract_all_data_from_file(filename)
        all_file_data.append(file_data)
        total_sections += file_data['total_sections']
        
        # Count total records
        for sheet_sections in file_data['sheets'].values():
            for section in sheet_sections:
                total_records += len(section['data'])
    
    print(f"\n📊 EXTRACTION SUMMARY:")
    print(f"   Files processed: {len(source_files)}")
    print(f"   Total sections: {total_sections}")
    print(f"   Total records: {total_records}")
    
    if total_records > 0:
        print(f"\n🔄 Creating comprehensive Excel file...")
        wb = create_comprehensive_excel(all_file_data)
        
        output_file = f"Comprehensive_CBE_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(output_file)
        
        print(f"\n✅ SUCCESS!")
        print(f"📁 Comprehensive report saved: {output_file}")
        print(f"📈 Total records included: {total_records}")
        print(f"📋 Total sections preserved: {total_sections}")
        print(f"🗂️  All file structures maintained")
        
    else:
        print("❌ No data found to consolidate!")

if __name__ == "__main__":
    main()
