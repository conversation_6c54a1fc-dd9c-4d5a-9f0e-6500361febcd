import pandas as pd
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import os
import glob
import warnings
from datetime import datetime
import re

warnings.filterwarnings('ignore')

def get_excel_files():
    """Get all Excel files except the template"""
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls")
    template_file = "SARITA DAGAR CBE REPORT.xlsx"
    excel_files = [f for f in all_files if f != template_file and not f.startswith('~')]
    return sorted(excel_files)

def clean_phone_number(phone_str):
    """Extract and clean phone numbers from text"""
    if pd.isna(phone_str) or phone_str == "":
        return ""
    
    phone_str = str(phone_str)
    # Extract phone numbers using regex
    phone_pattern = r'[6-9]\d{9}'
    phones = re.findall(phone_pattern, phone_str)
    
    if phones:
        return phones[0]  # Return first valid phone number
    return ""

def extract_participant_info(participant_str):
    """Extract participant name and phone number"""
    if pd.isna(participant_str) or participant_str == "":
        return "", ""
    
    participant_str = str(participant_str)
    
    # Try to split by common separators
    if ' - ' in participant_str:
        parts = participant_str.split(' - ')
        name = parts[0].strip()
        phone = clean_phone_number(parts[1]) if len(parts) > 1 else ""
    elif '=' in participant_str:
        parts = participant_str.split('=')
        name = parts[0].strip()
        phone = clean_phone_number(parts[1]) if len(parts) > 1 else ""
    elif ',' in participant_str:
        parts = participant_str.split(',')
        name = parts[0].strip()
        phone = clean_phone_number(parts[1]) if len(parts) > 1 else ""
    else:
        # Try to extract phone from the string
        phone = clean_phone_number(participant_str)
        if phone:
            # Remove phone from name
            name = re.sub(r'[6-9]\d{9}', '', participant_str).strip()
            name = re.sub(r'[-=,\s]+$', '', name).strip()
        else:
            name = participant_str.strip()
            phone = ""
    
    return name, phone

def standardize_date(date_val):
    """Standardize date format"""
    if pd.isna(date_val) or date_val == "":
        return ""
    
    if isinstance(date_val, datetime):
        return date_val.strftime('%Y-%m-%d')
    
    date_str = str(date_val)
    
    # Try different date formats
    date_formats = [
        '%Y-%m-%d %H:%M:%S',
        '%Y-%m-%d',
        '%d.%m.%Y',
        '%d.%m.%y',
        '%d/%m/%Y',
        '%d/%m/%y',
        '%d-%m-%Y',
        '%d-%m-%y'
    ]
    
    for fmt in date_formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt)
            return parsed_date.strftime('%Y-%m-%d')
        except:
            continue
    
    return date_str

def process_single_file(filename):
    """Process a single Excel file and extract standardized data"""
    print(f"Processing: {filename}")
    
    try:
        # Read all sheets
        df_dict = pd.read_excel(filename, sheet_name=None)
        
        all_records = []
        
        for sheet_name, df in df_dict.items():
            if df.empty:
                continue
                
            print(f"  Processing sheet: {sheet_name} ({df.shape[0]} rows)")
            
            # Try to identify the data structure
            records = extract_records_from_sheet(df, filename, sheet_name)
            all_records.extend(records)
        
        return all_records
        
    except Exception as e:
        print(f"Error processing {filename}: {e}")
        return []

def extract_records_from_sheet(df, filename, sheet_name):
    """Extract records from a sheet based on common patterns"""
    records = []
    
    # Convert all columns to string for easier processing
    df = df.astype(str)
    
    # Look for header patterns
    header_row = find_header_row(df)
    
    if header_row is None:
        print(f"    No clear header found in {sheet_name}")
        return records
    
    # Extract column mappings
    col_mapping = map_columns(df.iloc[header_row])
    
    # Process data rows
    for idx in range(header_row + 1, len(df)):
        row = df.iloc[idx]
        
        # Skip empty rows
        if row.isna().all() or (row == '').all():
            continue
            
        record = extract_record_from_row(row, col_mapping, filename)
        if record and any(record.values()):  # Only add if record has some data
            records.append(record)
    
    return records

def find_header_row(df):
    """Find the row that contains headers"""
    header_keywords = ['sno', 's.no', 'awc', 'aww', 'date', 'event', 'participant']
    
    for idx, row in df.iterrows():
        row_str = ' '.join(str(cell).lower() for cell in row if pd.notna(cell))
        
        # Count how many header keywords are found
        keyword_count = sum(1 for keyword in header_keywords if keyword in row_str)
        
        if keyword_count >= 3:  # At least 3 keywords found
            return idx
    
    return None

def map_columns(header_row):
    """Map columns based on header content"""
    mapping = {
        'sno': None,
        'awc_no': None,
        'aww_name': None,
        'date': None,
        'event_name': None,
        'participant_name': None,
        'phone': None,
        'remarks': None
    }
    
    for idx, header in enumerate(header_row):
        if pd.isna(header):
            continue
            
        header_lower = str(header).lower()
        
        if 'sno' in header_lower or 's.no' in header_lower:
            mapping['sno'] = idx
        elif 'awc' in header_lower and 'no' in header_lower:
            mapping['awc_no'] = idx
        elif 'aww' in header_lower or 'worker' in header_lower:
            mapping['aww_name'] = idx
        elif 'date' in header_lower:
            mapping['date'] = idx
        elif 'event' in header_lower and 'name' in header_lower:
            mapping['event_name'] = idx
        elif 'participant' in header_lower:
            if 'phone' in header_lower or 'no' in header_lower:
                mapping['participant_name'] = idx  # Combined field
            else:
                mapping['participant_name'] = idx
        elif 'phone' in header_lower:
            mapping['phone'] = idx
        elif 'remark' in header_lower:
            mapping['remarks'] = idx
    
    return mapping

def extract_record_from_row(row, col_mapping, filename):
    """Extract a standardized record from a row"""
    record = {
        'source_file': filename,
        'sno': '',
        'awc_no': '',
        'aww_name': '',
        'date': '',
        'event_name': '',
        'participant_name': '',
        'phone': '',
        'remarks': ''
    }
    
    for field, col_idx in col_mapping.items():
        if col_idx is not None and col_idx < len(row):
            value = row.iloc[col_idx] if hasattr(row, 'iloc') else row[col_idx]
            
            if pd.notna(value) and str(value) != 'nan' and str(value).strip() != '':
                if field == 'date':
                    record[field] = standardize_date(value)
                elif field == 'participant_name' and col_mapping['phone'] is None:
                    # Extract both name and phone from combined field
                    name, phone = extract_participant_info(value)
                    record['participant_name'] = name
                    record['phone'] = phone
                else:
                    record[field] = str(value).strip()
    
    return record

def create_consolidated_excel(all_records):
    """Create the consolidated Excel file with professional formatting"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Consolidated CBE Report"
    
    # Define styles
    header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    data_font = Font(name='Arial', size=10)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Headers
    headers = [
        'S.No.',
        'No. of AWC',
        'Name of AWW',
        'Date of Event',
        'Name of Event',
        'Name of Participants',
        'Phone Number',
        'Source File',
        'Remarks'
    ]
    
    # Set headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Set column widths
    column_widths = [8, 12, 20, 15, 25, 25, 15, 25, 20]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
    
    # Add data
    row_num = 2
    for i, record in enumerate(all_records, 1):
        data_row = [
            i,
            record.get('awc_no', ''),
            record.get('aww_name', ''),
            record.get('date', ''),
            record.get('event_name', ''),
            record.get('participant_name', ''),
            record.get('phone', ''),
            record.get('source_file', ''),
            record.get('remarks', '')
        ]
        
        for col, value in enumerate(data_row, 1):
            cell = ws.cell(row=row_num, column=col, value=value)
            cell.font = data_font
            cell.alignment = data_alignment
            cell.border = border
        
        row_num += 1
    
    # Set row height
    for row in range(1, row_num):
        ws.row_dimensions[row].height = 25
    
    return wb

def main():
    print("Starting Excel consolidation process...")
    
    excel_files = get_excel_files()
    print(f"Found {len(excel_files)} files to process")
    
    all_records = []
    
    for filename in excel_files:
        records = process_single_file(filename)
        all_records.extend(records)
        print(f"  Extracted {len(records)} records")
    
    print(f"\nTotal records extracted: {len(all_records)}")
    
    if all_records:
        print("Creating consolidated Excel file...")
        wb = create_consolidated_excel(all_records)
        
        output_filename = f"Consolidated_CBE_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(output_filename)
        
        print(f"Consolidated file saved as: {output_filename}")
        print(f"Total records: {len(all_records)}")
    else:
        print("No records found to consolidate!")

if __name__ == "__main__":
    main()
