import pandas as pd
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import os
import glob
import warnings
from datetime import datetime
import re

warnings.filterwarnings('ignore')

def get_excel_files():
    """Get all Excel files except the template and filter out duplicates"""
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls")
    template_file = "SARITA DAGAR CBE REPORT.xlsx"

    # Filter out template and temp files
    excel_files = [f for f in all_files if f != template_file and not f.startswith('~')]

    # Remove obvious duplicates based on file names
    filtered_files = filter_duplicate_files(excel_files)

    return sorted(filtered_files)

def filter_duplicate_files(file_list):
    """Filter out duplicate files based on naming patterns and content similarity"""

    # Define patterns for likely duplicates
    duplicate_patterns = [
        'untitled spreadsheet',
        'copy of',
        'book1',
        'sheet1'
    ]

    # Group files by potential duplicates
    unique_files = []
    processed_names = set()

    # Sort files to prioritize original files over copies
    sorted_files = sorted(file_list, key=lambda x: (
        'copy' in x.lower(),  # Copies go last
        'untitled' in x.lower(),  # Untitled files go last
        len(x),  # Shorter names first (usually originals)
        x.lower()
    ))

    for file in sorted_files:
        file_lower = file.lower()

        # Check if this is likely a duplicate based on name
        is_duplicate = False
        base_name = extract_base_name(file_lower)

        if base_name in processed_names:
            print(f"Skipping duplicate file: {file}")
            is_duplicate = True
        else:
            # Check for specific duplicate patterns
            for pattern in duplicate_patterns:
                if pattern in file_lower:
                    # Check if we already have a similar file
                    similar_found = False
                    for existing in unique_files:
                        if are_files_similar(file, existing):
                            print(f"Skipping duplicate file: {file} (similar to {existing})")
                            similar_found = True
                            break

                    if similar_found:
                        is_duplicate = True
                        break

        if not is_duplicate:
            unique_files.append(file)
            processed_names.add(base_name)

    print(f"Filtered {len(file_list)} files down to {len(unique_files)} unique files")
    return unique_files

def extract_base_name(filename):
    """Extract base name for duplicate detection"""
    # Remove common prefixes/suffixes that indicate copies
    base = filename.lower()

    # Remove file extension
    base = base.replace('.xlsx', '').replace('.xls', '')

    # Remove copy indicators
    copy_indicators = ['copy of ', 'copy', '(1)', '(2)', '(3)', 'untitled spreadsheet']
    for indicator in copy_indicators:
        base = base.replace(indicator, '')

    # Clean up spaces and special characters
    base = re.sub(r'[^a-zA-Z0-9]', '', base)

    return base.strip()

def are_files_similar(file1, file2):
    """Check if two files are similar based on name patterns"""
    base1 = extract_base_name(file1)
    base2 = extract_base_name(file2)

    # If base names are very similar (allowing for small differences)
    if base1 == base2:
        return True

    # Check for common patterns that indicate duplicates
    if len(base1) > 3 and len(base2) > 3:
        # Check if one is a substring of the other
        if base1 in base2 or base2 in base1:
            return True

    return False

def clean_phone_number(phone_str):
    """Extract and clean phone numbers from text"""
    if pd.isna(phone_str) or phone_str == "":
        return ""
    
    phone_str = str(phone_str)
    # Extract phone numbers using regex
    phone_pattern = r'[6-9]\d{9}'
    phones = re.findall(phone_pattern, phone_str)
    
    if phones:
        return phones[0]  # Return first valid phone number
    return ""

def extract_participant_info(participant_str):
    """Extract participant name and phone number"""
    if pd.isna(participant_str) or participant_str == "":
        return "", ""
    
    participant_str = str(participant_str)
    
    # Try to split by common separators
    if ' - ' in participant_str:
        parts = participant_str.split(' - ')
        name = parts[0].strip()
        phone = clean_phone_number(parts[1]) if len(parts) > 1 else ""
    elif '=' in participant_str:
        parts = participant_str.split('=')
        name = parts[0].strip()
        phone = clean_phone_number(parts[1]) if len(parts) > 1 else ""
    elif ',' in participant_str:
        parts = participant_str.split(',')
        name = parts[0].strip()
        phone = clean_phone_number(parts[1]) if len(parts) > 1 else ""
    else:
        # Try to extract phone from the string
        phone = clean_phone_number(participant_str)
        if phone:
            # Remove phone from name
            name = re.sub(r'[6-9]\d{9}', '', participant_str).strip()
            name = re.sub(r'[-=,\s]+$', '', name).strip()
        else:
            name = participant_str.strip()
            phone = ""
    
    return name, phone

def standardize_date(date_val):
    """Standardize date format"""
    if pd.isna(date_val) or date_val == "":
        return ""
    
    if isinstance(date_val, datetime):
        return date_val.strftime('%Y-%m-%d')
    
    date_str = str(date_val)
    
    # Try different date formats
    date_formats = [
        '%Y-%m-%d %H:%M:%S',
        '%Y-%m-%d',
        '%d.%m.%Y',
        '%d.%m.%y',
        '%d/%m/%Y',
        '%d/%m/%y',
        '%d-%m-%Y',
        '%d-%m-%y'
    ]
    
    for fmt in date_formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt)
            return parsed_date.strftime('%Y-%m-%d')
        except:
            continue
    
    return date_str

def process_single_file(filename):
    """Process a single Excel file and extract standardized data"""
    print(f"Processing: {filename}")

    try:
        # Read all sheets
        df_dict = pd.read_excel(filename, sheet_name=None)

        all_records = []

        for sheet_name, df in df_dict.items():
            if df.empty:
                continue

            print(f"  Processing sheet: {sheet_name} ({df.shape[0]} rows)")

            # Try to identify the data structure
            records = extract_records_from_sheet(df, filename, sheet_name)
            all_records.extend(records)

        return all_records

    except Exception as e:
        print(f"Error processing {filename}: {e}")
        return []

def remove_duplicate_records(all_records):
    """Remove duplicate records based on content similarity"""
    print("Removing duplicate records...")

    unique_records = []
    seen_combinations = set()

    for record in all_records:
        # Create a signature for the record
        signature = create_record_signature(record)

        if signature not in seen_combinations:
            unique_records.append(record)
            seen_combinations.add(signature)
        else:
            print(f"  Skipping duplicate record: {record.get('aww_name', '')} - {record.get('event_name', '')} - {record.get('date', '')}")

    print(f"Removed {len(all_records) - len(unique_records)} duplicate records")
    return unique_records

def create_record_signature(record):
    """Create a unique signature for a record to detect duplicates"""
    # Use key fields to create a signature
    key_fields = [
        record.get('awc_no', '').strip().lower(),
        record.get('aww_name', '').strip().lower(),
        record.get('date', '').strip(),
        record.get('event_name', '').strip().lower(),
        record.get('participant_name', '').strip().lower()
    ]

    # Clean and normalize the signature
    signature = '|'.join(field for field in key_fields if field)
    return signature

def extract_records_from_sheet(df, filename, sheet_name):
    """Extract records from a sheet based on common patterns"""
    records = []
    
    # Convert all columns to string for easier processing
    df = df.astype(str)
    
    # Look for header patterns
    header_row = find_header_row(df)
    
    if header_row is None:
        print(f"    No clear header found in {sheet_name}")
        return records
    
    # Extract column mappings
    col_mapping = map_columns(df.iloc[header_row])
    
    # Process data rows
    for idx in range(header_row + 1, len(df)):
        row = df.iloc[idx]
        
        # Skip empty rows
        if row.isna().all() or (row == '').all():
            continue
            
        record = extract_record_from_row(row, col_mapping, filename)
        if record and any(record.values()):  # Only add if record has some data
            records.append(record)
    
    return records

def find_header_row(df):
    """Find the row that contains headers"""
    header_keywords = ['sno', 's.no', 'awc', 'aww', 'date', 'event', 'participant']
    
    for idx, row in df.iterrows():
        row_str = ' '.join(str(cell).lower() for cell in row if pd.notna(cell))
        
        # Count how many header keywords are found
        keyword_count = sum(1 for keyword in header_keywords if keyword in row_str)
        
        if keyword_count >= 3:  # At least 3 keywords found
            return idx
    
    return None

def map_columns(header_row):
    """Map columns based on header content"""
    mapping = {
        'sno': None,
        'awc_no': None,
        'aww_name': None,
        'date': None,
        'event_name': None,
        'participant_name': None,
        'phone': None,
        'remarks': None
    }
    
    for idx, header in enumerate(header_row):
        if pd.isna(header):
            continue
            
        header_lower = str(header).lower()
        
        if 'sno' in header_lower or 's.no' in header_lower:
            mapping['sno'] = idx
        elif 'awc' in header_lower and 'no' in header_lower:
            mapping['awc_no'] = idx
        elif 'aww' in header_lower or 'worker' in header_lower:
            mapping['aww_name'] = idx
        elif 'date' in header_lower:
            mapping['date'] = idx
        elif 'event' in header_lower and 'name' in header_lower:
            mapping['event_name'] = idx
        elif 'participant' in header_lower:
            if 'phone' in header_lower or 'no' in header_lower:
                mapping['participant_name'] = idx  # Combined field
            else:
                mapping['participant_name'] = idx
        elif 'phone' in header_lower:
            mapping['phone'] = idx
        elif 'remark' in header_lower:
            mapping['remarks'] = idx
    
    return mapping

def extract_record_from_row(row, col_mapping, filename):
    """Extract a standardized record from a row"""
    record = {
        'source_file': filename,
        'sno': '',
        'awc_no': '',
        'aww_name': '',
        'date': '',
        'event_name': '',
        'participant_name': '',
        'phone': '',
        'remarks': ''
    }
    
    for field, col_idx in col_mapping.items():
        if col_idx is not None and col_idx < len(row):
            value = row.iloc[col_idx] if hasattr(row, 'iloc') else row[col_idx]
            
            if pd.notna(value) and str(value) != 'nan' and str(value).strip() != '':
                if field == 'date':
                    record[field] = standardize_date(value)
                elif field == 'participant_name' and col_mapping['phone'] is None:
                    # Extract both name and phone from combined field
                    name, phone = extract_participant_info(value)
                    record['participant_name'] = name
                    record['phone'] = phone
                else:
                    record[field] = str(value).strip()
    
    return record

def create_consolidated_excel(all_records):
    """Create the consolidated Excel file with professional formatting"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Consolidated CBE Report"
    
    # Define styles
    header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    data_font = Font(name='Arial', size=10)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Headers
    headers = [
        'S.No.',
        'No. of AWC',
        'Name of AWW',
        'Date of Event',
        'Name of Event',
        'Name of Participants',
        'Phone Number',
        'Source File',
        'Remarks'
    ]
    
    # Set headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Set column widths
    column_widths = [8, 12, 20, 15, 25, 25, 15, 25, 20]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
    
    # Add data
    row_num = 2
    for i, record in enumerate(all_records, 1):
        data_row = [
            i,
            record.get('awc_no', ''),
            record.get('aww_name', ''),
            record.get('date', ''),
            record.get('event_name', ''),
            record.get('participant_name', ''),
            record.get('phone', ''),
            record.get('source_file', ''),
            record.get('remarks', '')
        ]
        
        for col, value in enumerate(data_row, 1):
            cell = ws.cell(row=row_num, column=col, value=value)
            cell.font = data_font
            cell.alignment = data_alignment
            cell.border = border
        
        row_num += 1
    
    # Set row height
    for row in range(1, row_num):
        ws.row_dimensions[row].height = 25
    
    return wb

def main():
    print("Starting Excel consolidation process...")

    excel_files = get_excel_files()
    print(f"Found {len(excel_files)} unique files to process")

    # Print the files that will be processed
    print("\nFiles to be processed:")
    for i, filename in enumerate(excel_files, 1):
        print(f"  {i:2d}. {filename}")

    all_records = []

    for filename in excel_files:
        records = process_single_file(filename)
        all_records.extend(records)
        print(f"  Extracted {len(records)} records")

    print(f"\nTotal records extracted: {len(all_records)}")

    # Remove duplicate records
    if all_records:
        unique_records = remove_duplicate_records(all_records)

        print("Creating consolidated Excel file...")
        wb = create_consolidated_excel(unique_records)

        output_filename = f"Consolidated_CBE_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(output_filename)

        print(f"\nConsolidated file saved as: {output_filename}")
        print(f"Total unique records: {len(unique_records)}")
        print(f"Duplicate records removed: {len(all_records) - len(unique_records)}")

        # Create summary report
        create_summary_report(unique_records, excel_files, output_filename)
    else:
        print("No records found to consolidate!")

def create_summary_report(records, source_files, output_filename):
    """Create a summary report of the consolidation"""
    summary_filename = output_filename.replace('.xlsx', '_Summary.txt')

    with open(summary_filename, 'w', encoding='utf-8') as f:
        f.write("CONSOLIDATED CBE REPORT SUMMARY\n")
        f.write("=" * 50 + "\n\n")

        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Output file: {output_filename}\n\n")

        f.write(f"SOURCE FILES PROCESSED ({len(source_files)}):\n")
        f.write("-" * 30 + "\n")
        for i, filename in enumerate(source_files, 1):
            f.write(f"{i:2d}. {filename}\n")

        f.write(f"\nRECORDS SUMMARY:\n")
        f.write("-" * 20 + "\n")
        f.write(f"Total unique records: {len(records)}\n")

        # Count by source file
        file_counts = {}
        for record in records:
            source = record.get('source_file', 'Unknown')
            file_counts[source] = file_counts.get(source, 0) + 1

        f.write(f"\nRECORDS BY SOURCE FILE:\n")
        f.write("-" * 25 + "\n")
        for filename, count in sorted(file_counts.items()):
            f.write(f"{filename}: {count} records\n")

        # Count by AWW
        aww_counts = {}
        for record in records:
            aww = record.get('aww_name', 'Unknown').strip()
            if aww and aww != 'Unknown':
                aww_counts[aww] = aww_counts.get(aww, 0) + 1

        f.write(f"\nRECORDS BY AWW (Top 10):\n")
        f.write("-" * 25 + "\n")
        sorted_aww = sorted(aww_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        for aww, count in sorted_aww:
            f.write(f"{aww}: {count} records\n")

    print(f"Summary report saved as: {summary_filename}")

if __name__ == "__main__":
    main()
