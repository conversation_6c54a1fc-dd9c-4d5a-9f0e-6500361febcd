import pandas as pd
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import os
import glob
import warnings
from datetime import datetime
import re

warnings.filterwarnings('ignore')

def get_unique_excel_files():
    """Get unique Excel files, filtering out obvious duplicates"""
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls")
    template_file = "SARITA DAGAR CBE REPORT.xlsx"
    
    # Filter out template and temp files
    excel_files = [f for f in all_files if f != template_file and not f.startswith('~')]
    
    # Remove obvious duplicates
    unique_files = []
    skip_patterns = ['untitled spreadsheet1', 'untitled spreadsheet2', 'copy of']
    
    for file in sorted(excel_files):
        file_lower = file.lower()
        should_skip = False
        
        # Skip obvious duplicates
        for pattern in skip_patterns:
            if pattern in file_lower:
                print(f"Skipping duplicate file: {file}")
                should_skip = True
                break
        
        if not should_skip:
            unique_files.append(file)
    
    return unique_files

def clean_text(text):
    """Clean and normalize text"""
    if pd.isna(text) or text == "" or str(text).lower() == 'nan':
        return ""
    return str(text).strip()

def extract_phone(text):
    """Extract phone number from text"""
    if not text:
        return ""
    
    # Find 10-digit phone numbers starting with 6-9
    phone_match = re.search(r'[6-9]\d{9}', str(text))
    return phone_match.group() if phone_match else ""

def standardize_date(date_val):
    """Standardize date format"""
    if pd.isna(date_val) or date_val == "":
        return ""
    
    if isinstance(date_val, datetime):
        return date_val.strftime('%Y-%m-%d')
    
    date_str = str(date_val)
    
    # Common date patterns
    patterns = [
        r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
        r'(\d{1,2})\.(\d{1,2})\.(\d{4})',  # DD.MM.YYYY
        r'(\d{1,2})/(\d{1,2})/(\d{4})',   # DD/MM/YYYY
    ]
    
    for pattern in patterns:
        match = re.search(pattern, date_str)
        if match:
            try:
                if pattern == patterns[0]:  # YYYY-MM-DD
                    return f"{match.group(1)}-{match.group(2):0>2}-{match.group(3):0>2}"
                else:  # DD.MM.YYYY or DD/MM/YYYY
                    day, month, year = match.groups()
                    return f"{year}-{month:0>2}-{day:0>2}"
            except:
                continue
    
    return date_str[:10] if len(date_str) > 10 else date_str

def process_file_simple(filename):
    """Process file with simplified approach"""
    print(f"Processing: {filename}")
    records = []
    
    try:
        # Read first sheet only to avoid complexity
        df = pd.read_excel(filename, sheet_name=0)
        
        if df.empty:
            return records
        
        print(f"  Sheet has {df.shape[0]} rows, {df.shape[1]} columns")
        
        # Convert to string to avoid type issues
        df = df.astype(str)
        
        # Find data rows (skip headers and empty rows)
        for idx, row in df.iterrows():
            if idx > 50:  # Limit processing to avoid infinite loops
                break
                
            # Skip if row is mostly empty
            non_empty_cells = sum(1 for cell in row if cell and str(cell).strip() and str(cell).lower() != 'nan')
            if non_empty_cells < 3:
                continue
            
            # Try to extract meaningful data
            record = extract_record_simple(row, filename)
            if record:
                records.append(record)
        
        print(f"  Extracted {len(records)} records")
        return records
        
    except Exception as e:
        print(f"  Error processing {filename}: {e}")
        return records

def extract_record_simple(row, filename):
    """Extract record with simple heuristics"""
    row_values = [clean_text(cell) for cell in row]
    
    # Skip header-like rows
    row_text = ' '.join(row_values).lower()
    if any(keyword in row_text for keyword in ['sno', 'awc', 'aww', 'date', 'event', 'participant']):
        return None
    
    # Try to find meaningful data
    awc_no = ""
    aww_name = ""
    date = ""
    event_name = ""
    participant_name = ""
    phone = ""
    
    for i, cell in enumerate(row_values):
        if not cell:
            continue
            
        # Look for AWC number (usually numeric)
        if cell.isdigit() and len(cell) <= 3 and not awc_no:
            awc_no = cell
        
        # Look for date
        if re.search(r'\d{4}|\d{1,2}[./]\d{1,2}', cell) and not date:
            date = standardize_date(cell)
        
        # Look for phone number
        phone_found = extract_phone(cell)
        if phone_found and not phone:
            phone = phone_found
            # Extract name from the same cell
            name_part = re.sub(r'[6-9]\d{9}', '', cell).strip()
            name_part = re.sub(r'[-=,\s]+', ' ', name_part).strip()
            if name_part and not participant_name:
                participant_name = name_part
        
        # Look for common event names
        if any(event in cell.lower() for event in ['handwash', 'godbharai', 'annaprashan', 'pre-school']) and not event_name:
            event_name = cell
        
        # Look for names (contains alphabets, reasonable length)
        if re.match(r'^[a-zA-Z\s]+$', cell) and 3 <= len(cell) <= 30:
            if not aww_name and i < 5:  # AWW name usually in first few columns
                aww_name = cell
            elif not participant_name and phone:  # Participant name near phone
                participant_name = cell
    
    # Only return record if we have some meaningful data
    if awc_no or aww_name or participant_name:
        return {
            'source_file': filename,
            'awc_no': awc_no,
            'aww_name': aww_name,
            'date': date,
            'event_name': event_name,
            'participant_name': participant_name,
            'phone': phone,
            'remarks': ''
        }
    
    return None

def create_excel_output(records):
    """Create formatted Excel output"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Consolidated CBE Report"
    
    # Styles
    header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    data_font = Font(name='Arial', size=10)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    # Headers
    headers = ['S.No.', 'No. of AWC', 'Name of AWW', 'Date of Event', 
               'Name of Event', 'Name of Participants', 'Phone Number', 'Source File', 'Remarks']
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Column widths
    widths = [8, 12, 20, 15, 25, 25, 15, 25, 20]
    for col, width in enumerate(widths, 1):
        ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
    
    # Data rows
    for i, record in enumerate(records, 1):
        row_data = [
            i, record.get('awc_no', ''), record.get('aww_name', ''),
            record.get('date', ''), record.get('event_name', ''),
            record.get('participant_name', ''), record.get('phone', ''),
            record.get('source_file', ''), record.get('remarks', '')
        ]
        
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=i+1, column=col, value=value)
            cell.font = data_font
            cell.alignment = data_alignment
            cell.border = border
    
    return wb

def main():
    print("Starting efficient Excel consolidation...")
    
    files = get_unique_excel_files()
    print(f"Processing {len(files)} unique files:")
    
    for i, f in enumerate(files, 1):
        print(f"  {i:2d}. {f}")
    
    all_records = []
    
    for filename in files:
        records = process_file_simple(filename)
        all_records.extend(records)
    
    print(f"\nTotal records extracted: {len(all_records)}")
    
    if all_records:
        # Remove basic duplicates
        unique_records = []
        seen = set()
        
        for record in all_records:
            key = f"{record.get('awc_no', '')}-{record.get('aww_name', '')}-{record.get('participant_name', '')}-{record.get('phone', '')}"
            if key not in seen:
                unique_records.append(record)
                seen.add(key)
        
        print(f"Unique records after deduplication: {len(unique_records)}")
        
        wb = create_excel_output(unique_records)
        output_file = f"Consolidated_CBE_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(output_file)
        
        print(f"\nConsolidated file saved: {output_file}")
        print(f"Total records: {len(unique_records)}")
    else:
        print("No records found!")

if __name__ == "__main__":
    main()
