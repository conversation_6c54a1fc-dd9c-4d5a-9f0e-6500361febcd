import pandas as pd
import openpyxl
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.merge import MergedCell
import os
import glob
import warnings
from datetime import datetime
import re
from PIL import Image as PILImage
import io
import tempfile

warnings.filterwarnings('ignore')

def get_source_files():
    """Get all source Excel files excluding template and generated files"""
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls")
    
    exclude_files = ["SARITA DAGAR CBE REPORT.xlsx"]
    exclude_patterns = [
        'consolidated_cbe_report', 'professional_cbe_report', 
        'comprehensive_cbe_report', 'final_complete_cbe_report',
        'exact_template_cbe_report'
    ]
    
    source_files = []
    for file in sorted(all_files):
        if file in exclude_files:
            continue
        
        file_lower = file.lower()
        should_skip = any(pattern in file_lower for pattern in exclude_patterns)
        
        if not should_skip:
            source_files.append(file)
    
    return source_files

def extract_images_from_file(filename):
    """Extract images from Excel file"""
    images = []
    try:
        wb = load_workbook(filename)
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            if hasattr(ws, '_images') and ws._images:
                for img in ws._images:
                    try:
                        # Get image data
                        img_data = img._data()
                        if img_data:
                            images.append({
                                'data': img_data,
                                'sheet': sheet_name,
                                'anchor': str(img.anchor) if hasattr(img, 'anchor') else 'A1',
                                'filename': filename
                            })
                    except Exception as e:
                        print(f"    Error extracting image: {e}")
        wb.close()
    except Exception as e:
        print(f"  Error loading images from {filename}: {e}")
    
    return images

def process_file_data(filename):
    """Process file and extract structured data"""
    print(f"Processing: {filename}")
    
    # Extract images
    images = extract_images_from_file(filename)
    print(f"  Found {len(images)} images")
    
    records = []
    
    try:
        # Read file data
        if filename.endswith('.xls'):
            sheets_dict = pd.read_excel(filename, sheet_name=None, engine='xlrd')
        else:
            sheets_dict = pd.read_excel(filename, sheet_name=None)
        
        for sheet_name, df in sheets_dict.items():
            if df.empty:
                continue
                
            print(f"  Processing sheet: {sheet_name} ({df.shape[0]} rows)")
            
            # Find data rows
            for idx, row in df.iterrows():
                if idx > 500:  # Limit processing
                    break
                
                # Convert to strings
                row_data = [str(cell).strip() if pd.notna(cell) and str(cell).lower() != 'nan' else '' 
                           for cell in row]
                
                # Skip empty rows
                if not any(row_data):
                    continue
                
                # Skip header rows
                row_text = ' '.join(row_data).lower()
                if any(keyword in row_text for keyword in ['sno', 'awc', 'aww', 'date', 'event', 'participant']):
                    continue
                
                # Extract structured data
                record = extract_structured_data(row_data, filename, sheet_name, images)
                if record and (record['aww_name'] or record['participant_name'] or record['awc_no']):
                    records.append(record)
        
        print(f"  Extracted {len(records)} records")
        return records, images
        
    except Exception as e:
        print(f"  Error: {e}")
        return [], []

def extract_structured_data(row_data, filename, sheet_name, images):
    """Extract structured data from row"""
    record = {
        'source_file': filename,
        'sheet_name': sheet_name,
        'awc_no': '',
        'aww_name': '',
        'date': '',
        'event_name': '',
        'participant_name': '',
        'phone': '',
        'remarks': '',
        'images': []
    }
    
    for i, cell in enumerate(row_data):
        if not cell:
            continue
        
        # AWC number
        if cell.isdigit() and 1 <= len(cell) <= 3 and not record['awc_no']:
            record['awc_no'] = cell
        
        # Date
        elif re.search(r'\d{4}|\d{1,2}[./]\d{1,2}', cell):
            record['date'] = standardize_date(cell)
        
        # Phone and participant
        elif re.search(r'[6-9]\d{9}', cell):
            name, phone = extract_name_phone(cell)
            if name:
                record['participant_name'] = name
            if phone:
                record['phone'] = phone
        
        # Event name
        elif any(event in cell.lower() for event in [
            'handwash', 'hand wash', 'godbharai', 'god bharai', 'annaprashan', 
            'pre-school', 'preschool', 'breast feeding'
        ]):
            record['event_name'] = cell
        
        # AWW name
        elif (re.match(r'^[a-zA-Z\s]+$', cell) and 3 <= len(cell) <= 30 and 
              i < 5 and not record['aww_name']):
            record['aww_name'] = cell
        
        # Participant name (fallback)
        elif (re.match(r'^[a-zA-Z\s]+$', cell) and 3 <= len(cell) <= 30 and 
              not record['participant_name'] and record['aww_name']):
            record['participant_name'] = cell
    
    # Associate images (simple association - all images for now)
    record['images'] = images[:1] if images else []  # Take first image if available
    
    return record

def extract_name_phone(text):
    """Extract name and phone from text"""
    if not text:
        return "", ""
    
    phone_match = re.search(r'[6-9]\d{9}', text)
    phone = phone_match.group() if phone_match else ""
    
    name = text
    if phone:
        name = re.sub(r'[6-9]\d{9}', '', name)
    
    name = re.sub(r'[-=,\s]+', ' ', name).strip()
    name = re.sub(r'^[^a-zA-Z]*', '', name)
    name = re.sub(r'[^a-zA-Z\s]*$', '', name)
    
    return name.strip(), phone

def standardize_date(date_val):
    """Standardize date format"""
    if not date_val:
        return ""
    
    if isinstance(date_val, datetime):
        return date_val.strftime('%Y-%m-%d')
    
    date_str = str(date_val).strip()
    
    patterns = [
        (r'(\d{4})-(\d{1,2})-(\d{1,2})', lambda m: f"{m.group(1)}-{m.group(2):0>2}-{m.group(3):0>2}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{4})', lambda m: f"{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
        (r'(\d{1,2})/(\d{1,2})/(\d{4})', lambda m: f"{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{2})', lambda m: f"20{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
    ]
    
    for pattern, formatter in patterns:
        match = re.search(pattern, date_str)
        if match:
            try:
                return formatter(match)
            except:
                continue
    
    return date_str

def create_exact_template_excel(all_records, all_images):
    """Create Excel file with exact template structure"""
    wb = Workbook()
    ws = wb.active
    ws.title = "CBE Report"
    
    # Define styles
    header_font = Font(name='Arial', size=11, bold=True)
    data_font = Font(name='Arial', size=10)
    
    border_thin = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    alignment_center = Alignment(horizontal='center', vertical='center', wrap_text=True)
    alignment_left = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    # Row 1: Name of District
    ws.cell(row=1, column=1, value="Name of District:").font = header_font
    ws.cell(row=1, column=2, value="SOUTH WEST DELHI").font = data_font
    
    # Row 2: Name of Project  
    ws.cell(row=2, column=1, value="Name of Project:").font = header_font
    ws.cell(row=2, column=2, value="NAJAFGARH").font = data_font
    
    # Row 3: Month & Year
    ws.cell(row=3, column=1, value="Month & Year of activity held:").font = header_font
    ws.cell(row=3, column=2, value="MAY 2023 TO DECEMBER 2024").font = data_font
    
    # Empty row
    current_row = 5
    
    # Table headers (exactly as shown in image)
    headers = [
        "S.No.",
        "Number of AWC", 
        "Name of AWW",
        "Date of Event",
        "Name of Event along with name of reg. beneficiary concerned for the event",
        "Names of participants with Phone Number",
        "Photographs of participants and event",
        "Remarks"
    ]
    
    # Set headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=current_row, column=col, value=header)
        cell.font = header_font
        cell.alignment = alignment_center
        cell.border = border_thin
    
    # Set column widths to match template
    column_widths = [8, 15, 20, 15, 45, 30, 25, 20]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(col)].width = width
    
    # Set header row height
    ws.row_dimensions[current_row].height = 40
    
    current_row += 1
    
    # Add data rows
    for i, record in enumerate(all_records, 1):
        # Combine participant name and phone
        participant_info = record.get('participant_name', '')
        if record.get('phone'):
            if participant_info:
                participant_info += f" - {record['phone']}"
            else:
                participant_info = record['phone']
        
        row_data = [
            i,
            record.get('awc_no', ''),
            record.get('aww_name', ''),
            record.get('date', ''),
            record.get('event_name', ''),
            participant_info,
            '',  # Photographs - will add images here
            record.get('remarks', '')
        ]
        
        # Add data to cells
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=current_row, column=col, value=value)
            cell.font = data_font
            cell.alignment = alignment_left
            cell.border = border_thin
        
        # Add image if available
        if record.get('images'):
            try:
                img_info = record['images'][0]
                if 'data' in img_info:
                    # Create temporary image file
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                        tmp_file.write(img_info['data'])
                        tmp_file.flush()
                        
                        # Add image to Excel
                        img = Image(tmp_file.name)
                        # Resize image to fit cell
                        img.width = 100
                        img.height = 80
                        
                        # Position image in photographs column (column 7)
                        img.anchor = f'G{current_row}'
                        ws.add_image(img)
                        
                        # Clean up temp file
                        os.unlink(tmp_file.name)
            except Exception as e:
                print(f"Error adding image for row {i}: {e}")
        
        # Set row height to accommodate images
        ws.row_dimensions[current_row].height = 60
        current_row += 1
    
    return wb

def main():
    print("=== EXACT TEMPLATE CBE REPORT CONSOLIDATION ===")
    print("Creating Excel with exact structure as shown in image...\n")
    
    source_files = get_source_files()
    print(f"Processing {len(source_files)} files:\n")
    
    for i, filename in enumerate(source_files, 1):
        print(f"  {i:2d}. {filename}")
    
    print("\n" + "="*80)
    
    all_records = []
    all_images = []
    
    for filename in source_files:
        records, images = process_file_data(filename)
        all_records.extend(records)
        all_images.extend(images)
    
    print(f"\n📊 CONSOLIDATION SUMMARY:")
    print(f"   Files processed: {len(source_files)}")
    print(f"   Total records: {len(all_records)}")
    print(f"   Total images found: {len(all_images)}")
    
    if all_records:
        print(f"\n🔄 Creating Excel with exact template structure...")
        wb = create_exact_template_excel(all_records, all_images)
        
        output_file = f"Exact_Template_CBE_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(output_file)
        
        print(f"\n✅ SUCCESS!")
        print(f"📁 Report saved: {output_file}")
        print(f"📊 Records included: {len(all_records)}")
        print(f"🖼️  Images integrated: {len(all_images)}")
        print(f"📋 Exact template structure maintained")
        print(f"🎯 Professional formatting applied")
        
        # Statistics
        unique_awws = len(set(r['aww_name'] for r in all_records if r['aww_name']))
        unique_awcs = len(set(r['awc_no'] for r in all_records if r['awc_no']))
        
        print(f"\n📈 DATA STATISTICS:")
        print(f"   Unique AWWs: {unique_awws}")
        print(f"   Unique AWCs: {unique_awcs}")
        
    else:
        print("❌ No records found!")

if __name__ == "__main__":
    main()
