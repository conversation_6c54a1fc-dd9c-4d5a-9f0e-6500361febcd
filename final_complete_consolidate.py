import pandas as pd
import openpyxl
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
import os
import glob
import warnings
from datetime import datetime
import re

warnings.filterwarnings('ignore')

def get_all_source_files():
    """Get ALL original Excel files, excluding only the template"""
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls")
    
    # Only exclude the template file
    template_file = "SARITA DAGAR CBE REPORT.xlsx"
    
    # Also exclude our generated files
    exclude_patterns = [
        'consolidated_cbe_report',
        'professional_cbe_report', 
        'comprehensive_cbe_report',
        'final_complete_cbe_report'
    ]
    
    source_files = []
    
    for file in sorted(all_files):
        if file == template_file:
            continue
            
        file_lower = file.lower()
        should_skip = False
        
        for pattern in exclude_patterns:
            if pattern in file_lower:
                should_skip = True
                break
        
        if not should_skip:
            source_files.append(file)
    
    return source_files

def extract_complete_file_data(filename):
    """Extract complete data from file including all rows and structure"""
    print(f"Processing: {filename}")
    
    all_records = []
    images_info = []
    
    try:
        # Extract images information
        try:
            wb_img = load_workbook(filename)
            for sheet_name in wb_img.sheetnames:
                ws = wb_img[sheet_name]
                if hasattr(ws, '_images') and ws._images:
                    for img in ws._images:
                        images_info.append({
                            'sheet': sheet_name,
                            'anchor': str(img.anchor) if hasattr(img, 'anchor') else 'Unknown',
                            'filename': filename
                        })
            wb_img.close()
        except:
            pass
        
        print(f"  Found {len(images_info)} images")
        
        # Read all sheets
        try:
            if filename.endswith('.xls'):
                sheets_dict = pd.read_excel(filename, sheet_name=None, engine='xlrd')
            else:
                sheets_dict = pd.read_excel(filename, sheet_name=None)
        except:
            try:
                sheets_dict = {0: pd.read_excel(filename, sheet_name=0)}
            except:
                print(f"  Could not read {filename}")
                return []
        
        total_processed = 0
        
        for sheet_name, df in sheets_dict.items():
            if df.empty:
                continue
                
            print(f"  Sheet: {sheet_name} ({df.shape[0]} rows)")
            
            # Process every single row
            for idx, row in df.iterrows():
                # Convert row to strings and clean
                row_data = []
                for cell in row:
                    if pd.notna(cell) and str(cell).strip() and str(cell).lower() != 'nan':
                        row_data.append(str(cell).strip())
                    else:
                        row_data.append('')
                
                # Skip completely empty rows
                if not any(row_data):
                    continue
                
                # Create record for this row
                record = create_complete_record(row_data, filename, sheet_name, idx + 1)
                if record:
                    all_records.append(record)
                    total_processed += 1
        
        print(f"  Processed {total_processed} rows")
        return all_records
        
    except Exception as e:
        print(f"  Error: {e}")
        return []

def create_complete_record(row_data, filename, sheet_name, row_number):
    """Create a complete record from row data"""
    record = {
        'source_file': filename,
        'sheet_name': sheet_name,
        'row_number': row_number,
        'sno': '',
        'awc_no': '',
        'aww_name': '',
        'date': '',
        'event_name': '',
        'participant_name': '',
        'phone': '',
        'remarks': '',
        'raw_data': ' | '.join(row_data),
        'all_columns': row_data
    }
    
    # Analyze each cell in the row
    for i, cell in enumerate(row_data):
        if not cell:
            continue
        
        cell_lower = cell.lower()
        
        # Serial number detection
        if i == 0 and cell.isdigit() and len(cell) <= 4:
            record['sno'] = cell
        
        # AWC number detection
        elif cell.isdigit() and 1 <= len(cell) <= 3:
            if not record['awc_no']:
                record['awc_no'] = cell
        
        # Date detection
        elif re.search(r'\d{4}|\d{1,2}[./]\d{1,2}', cell):
            if not record['date']:
                record['date'] = standardize_date(cell)
        
        # Phone number detection and participant extraction
        elif re.search(r'[6-9]\d{9}', cell):
            name, phone = extract_name_phone(cell)
            if name and not record['participant_name']:
                record['participant_name'] = name
            if phone and not record['phone']:
                record['phone'] = phone
        
        # Event name detection
        elif any(event in cell_lower for event in [
            'handwash', 'hand wash', 'godbharai', 'god bharai', 'annaprashan', 
            'pre-school', 'preschool', 'breast feeding', 'breastfeeding'
        ]):
            if not record['event_name']:
                record['event_name'] = cell
        
        # AWW name detection (alphabetic, reasonable length, early in row)
        elif (re.match(r'^[a-zA-Z\s]+$', cell) and 3 <= len(cell) <= 30 and 
              i < 6 and not record['aww_name']):
            record['aww_name'] = cell
        
        # Participant name (if no phone found yet)
        elif (re.match(r'^[a-zA-Z\s]+$', cell) and 3 <= len(cell) <= 30 and 
              not record['participant_name'] and record['aww_name']):
            record['participant_name'] = cell
        
        # Remarks (usually last meaningful column)
        elif i > 5 and cell and not record['remarks']:
            record['remarks'] = cell
    
    return record

def extract_name_phone(text):
    """Extract name and phone from combined text"""
    if not text:
        return "", ""
    
    # Find phone number
    phone_match = re.search(r'[6-9]\d{9}', text)
    phone = phone_match.group() if phone_match else ""
    
    # Extract name by removing phone and cleaning
    name = text
    if phone:
        name = re.sub(r'[6-9]\d{9}', '', name)
    
    # Clean name
    name = re.sub(r'[-=,\s]+', ' ', name).strip()
    name = re.sub(r'^[^a-zA-Z]*', '', name)
    name = re.sub(r'[^a-zA-Z\s]*$', '', name)
    
    return name.strip(), phone

def standardize_date(date_val):
    """Standardize date format"""
    if not date_val:
        return ""
    
    if isinstance(date_val, datetime):
        return date_val.strftime('%Y-%m-%d')
    
    date_str = str(date_val).strip()
    
    # Try different date patterns
    patterns = [
        (r'(\d{4})-(\d{1,2})-(\d{1,2})', lambda m: f"{m.group(1)}-{m.group(2):0>2}-{m.group(3):0>2}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{4})', lambda m: f"{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
        (r'(\d{1,2})/(\d{1,2})/(\d{4})', lambda m: f"{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{2})', lambda m: f"20{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
    ]
    
    for pattern, formatter in patterns:
        match = re.search(pattern, date_str)
        if match:
            try:
                return formatter(match)
            except:
                continue
    
    return date_str

def create_final_excel(all_records):
    """Create final Excel file matching template format exactly"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Complete CBE Report"
    
    # Exact styling to match template
    header_font = Font(name='Calibri', size=11, bold=True, color='000000')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    data_font = Font(name='Calibri', size=10)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    # Headers exactly matching template
    headers = [
        'S no.',
        'No. of Awc', 
        'Name of Aww',
        'Date of Event',
        'Name of event alongwith name of reg.beneficiary concerned for the event',
        'Name of participants with phone no.',
        'Photographs of participants and event',
        'Remarks',
        'Source File',
        'Sheet Name',
        'Row Number',
        'Raw Data'
    ]
    
    # Set headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.alignment = header_alignment
        cell.border = border
    
    # Set column widths to match template
    column_widths = [8, 12, 18, 15, 40, 30, 20, 15, 25, 15, 10, 50]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(col)].width = width
    
    # Add all data
    for i, record in enumerate(all_records, 1):
        # Combine participant name and phone
        participant_info = record.get('participant_name', '')
        if record.get('phone'):
            if participant_info:
                participant_info += f" - {record['phone']}"
            else:
                participant_info = record['phone']
        
        row_data = [
            i,  # Sequential number
            record.get('awc_no', ''),
            record.get('aww_name', ''),
            record.get('date', ''),
            record.get('event_name', ''),
            participant_info,
            '',  # Photographs column (placeholder)
            record.get('remarks', ''),
            record.get('source_file', ''),
            record.get('sheet_name', ''),
            record.get('row_number', ''),
            record.get('raw_data', '')
        ]
        
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=i+1, column=col, value=value)
            cell.font = data_font
            cell.alignment = data_alignment
            cell.border = border
            
            # Set row height
            ws.row_dimensions[i+1].height = 30
    
    return wb

def main():
    print("=== FINAL COMPLETE CBE REPORT CONSOLIDATION ===")
    print("Including EVERY row from EVERY file...\n")
    
    source_files = get_all_source_files()
    print(f"Processing {len(source_files)} files:\n")
    
    for i, filename in enumerate(source_files, 1):
        print(f"  {i:2d}. {filename}")
    
    print("\n" + "="*80)
    
    all_records = []
    
    for filename in source_files:
        file_records = extract_complete_file_data(filename)
        all_records.extend(file_records)
    
    print(f"\n📊 FINAL SUMMARY:")
    print(f"   Files processed: {len(source_files)}")
    print(f"   Total rows captured: {len(all_records)}")
    
    if all_records:
        print(f"\n🔄 Creating final complete Excel file...")
        wb = create_final_excel(all_records)
        
        output_file = f"Final_Complete_CBE_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(output_file)
        
        print(f"\n✅ COMPLETE SUCCESS!")
        print(f"📁 Final report saved: {output_file}")
        print(f"📊 Total records: {len(all_records)}")
        print(f"🎯 Every row from every file included")
        print(f"📋 Professional formatting applied")
        print(f"🖼️  Image placeholders ready")
        
        # Statistics
        files_with_data = len(set(r['source_file'] for r in all_records))
        unique_awws = len(set(r['aww_name'] for r in all_records if r['aww_name']))
        unique_awcs = len(set(r['awc_no'] for r in all_records if r['awc_no']))
        
        print(f"\n📈 DATA STATISTICS:")
        print(f"   Files with data: {files_with_data}")
        print(f"   Unique AWWs: {unique_awws}")
        print(f"   Unique AWCs: {unique_awcs}")
        
    else:
        print("❌ No data found!")

if __name__ == "__main__":
    main()
