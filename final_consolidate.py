import pandas as pd
import openpyxl
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
import os
import glob
import warnings
from datetime import datetime
import re
from PIL import Image as PILImage
import io
import tempfile

warnings.filterwarnings('ignore')

def get_source_excel_files():
    """Get original Excel files, excluding template and generated files"""
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls")
    
    # Files to exclude
    exclude_files = [
        "SARITA DAGAR CBE REPORT.xlsx",  # Template file
    ]
    
    # Patterns to exclude
    exclude_patterns = [
        'consolidated_cbe_report',  # Previously generated files
        'untitled spreadsheet1',
        'untitled spreadsheet2', 
        'copy of book1',
        '~'  # Temp files
    ]
    
    source_files = []
    
    for file in sorted(all_files):
        file_lower = file.lower()
        
        # Skip excluded files
        if file in exclude_files:
            continue
            
        # Skip files matching exclude patterns
        should_skip = False
        for pattern in exclude_patterns:
            if pattern in file_lower:
                print(f"Skipping: {file}")
                should_skip = True
                break
        
        if not should_skip:
            source_files.append(file)
    
    return source_files

def extract_participant_data(text):
    """Extract participant name and phone from combined text"""
    if not text or pd.isna(text) or str(text).lower() == 'nan':
        return "", ""
    
    text = str(text).strip()
    
    # Extract phone number
    phone_match = re.search(r'[6-9]\d{9}', text)
    phone = phone_match.group() if phone_match else ""
    
    # Extract name (remove phone and clean up)
    name = text
    if phone:
        name = re.sub(r'[6-9]\d{9}', '', name)
    
    # Clean up name
    name = re.sub(r'[-=,\s]+', ' ', name).strip()
    name = re.sub(r'^[^a-zA-Z]*', '', name)  # Remove leading non-letters
    name = re.sub(r'[^a-zA-Z\s]*$', '', name)  # Remove trailing non-letters
    
    return name.strip(), phone

def standardize_date(date_val):
    """Convert various date formats to standard YYYY-MM-DD"""
    if pd.isna(date_val) or not date_val:
        return ""
    
    if isinstance(date_val, datetime):
        return date_val.strftime('%Y-%m-%d')
    
    date_str = str(date_val).strip()
    
    # Try different patterns
    patterns = [
        (r'(\d{4})-(\d{1,2})-(\d{1,2})', lambda m: f"{m.group(1)}-{m.group(2):0>2}-{m.group(3):0>2}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{4})', lambda m: f"{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
        (r'(\d{1,2})/(\d{1,2})/(\d{4})', lambda m: f"{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{2})', lambda m: f"20{m.group(3)}-{m.group(2):0>2}-{m.group(1):0>2}"),
    ]
    
    for pattern, formatter in patterns:
        match = re.search(pattern, date_str)
        if match:
            try:
                return formatter(match)
            except:
                continue
    
    return date_str[:10] if len(date_str) >= 10 else date_str

def extract_images_from_excel(filename):
    """Extract all images from an Excel file"""
    images = []
    try:
        wb = load_workbook(filename)
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            if hasattr(ws, '_images'):
                for img in ws._images:
                    try:
                        # Get image data
                        img_data = img._data()
                        if img_data:
                            images.append({
                                'data': img_data,
                                'anchor': img.anchor,
                                'sheet': sheet_name,
                                'filename': filename
                            })
                    except Exception as e:
                        print(f"    Error extracting image: {e}")
        wb.close()
    except Exception as e:
        print(f"  Error loading workbook for images: {e}")

    return images

def process_excel_file(filename):
    """Process a single Excel file and extract structured data with images"""
    print(f"Processing: {filename}")
    records = []
    images = []

    try:
        # Extract images first
        images = extract_images_from_excel(filename)
        print(f"  Found {len(images)} images")

        # Try to read all sheets
        try:
            sheets_dict = pd.read_excel(filename, sheet_name=None)
        except:
            # If multiple sheets fail, try first sheet only
            sheets_dict = {0: pd.read_excel(filename, sheet_name=0)}

        for sheet_name, df in sheets_dict.items():
            if df.empty:
                continue

            print(f"  Processing sheet: {sheet_name} ({df.shape[0]} rows)")

            # Find the header row
            header_row_idx = find_data_start(df)
            if header_row_idx is None:
                continue

            # Process data rows
            for idx in range(header_row_idx, min(header_row_idx + 200, len(df))):  # Limit to avoid huge files
                row = df.iloc[idx]

                # Skip empty rows
                if row.isna().all():
                    continue

                record = extract_record_from_row(row, filename)
                if record and (record['aww_name'] or record['participant_name'] or record['awc_no']):
                    # Try to associate images with this record
                    record['images'] = []
                    for img in images:
                        # Simple association - you might want to improve this logic
                        record['images'].append(img)
                    records.append(record)

        print(f"  Extracted {len(records)} records")
        return records, images

    except Exception as e:
        print(f"  Error: {e}")
        return [], []

def find_data_start(df):
    """Find where actual data starts (after headers)"""
    for idx in range(min(20, len(df))):  # Check first 20 rows
        row = df.iloc[idx]
        row_text = ' '.join(str(cell).lower() for cell in row if pd.notna(cell))
        
        # Look for header indicators
        if any(keyword in row_text for keyword in ['sno', 'awc', 'aww', 'date', 'event', 'participant']):
            return idx + 1  # Return next row as data start
    
    return 1  # Default to second row

def extract_record_from_row(row, filename):
    """Extract structured data from a row"""
    record = {
        'source_file': filename,
        'awc_no': '',
        'aww_name': '',
        'date': '',
        'event_name': '',
        'participant_name': '',
        'phone': '',
        'remarks': ''
    }
    
    row_values = [str(cell).strip() if pd.notna(cell) and str(cell).lower() != 'nan' else '' for cell in row]
    
    # Skip header-like rows
    row_text = ' '.join(row_values).lower()
    if any(keyword in row_text for keyword in ['sno', 'awc no', 'aww name', 'date of event']):
        return None
    
    for i, cell in enumerate(row_values):
        if not cell:
            continue
        
        # AWC Number (usually 1-3 digits)
        if cell.isdigit() and 1 <= len(cell) <= 3 and not record['awc_no']:
            record['awc_no'] = cell
        
        # Date detection
        if re.search(r'\d{4}|\d{1,2}[./]\d{1,2}', cell) and not record['date']:
            record['date'] = standardize_date(cell)
        
        # Event names
        event_keywords = ['handwash', 'godbharai', 'annaprashan', 'pre-school', 'breast feeding']
        if any(keyword in cell.lower() for keyword in event_keywords) and not record['event_name']:
            record['event_name'] = cell
        
        # Participant with phone
        if re.search(r'[6-9]\d{9}', cell):
            name, phone = extract_participant_data(cell)
            if name and not record['participant_name']:
                record['participant_name'] = name
            if phone and not record['phone']:
                record['phone'] = phone
        
        # AWW Name (alphabetic, reasonable length, early in row)
        elif (re.match(r'^[a-zA-Z\s]+$', cell) and 3 <= len(cell) <= 25 and 
              i < 6 and not record['aww_name']):
            record['aww_name'] = cell
        
        # Participant name (if no phone found yet)
        elif (re.match(r'^[a-zA-Z\s]+$', cell) and 3 <= len(cell) <= 25 and 
              not record['participant_name'] and record['aww_name']):
            record['participant_name'] = cell
    
    return record

def create_professional_excel(records):
    """Create professionally formatted Excel file"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Consolidated CBE Report"
    
    # Professional styling
    header_font = Font(name='Calibri', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='2F5597', end_color='2F5597', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    data_font = Font(name='Calibri', size=11)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    border = Border(
        left=Side(style='thin', color='000000'),
        right=Side(style='thin', color='000000'),
        top=Side(style='thin', color='000000'),
        bottom=Side(style='thin', color='000000')
    )
    
    # Headers matching template format
    headers = [
        'S no.',
        'No. of Awc',
        'Name of Aww',
        'Date of Event',
        'Name of event alongwith name of reg.beneficiary concerned for the event',
        'Name of participants with phone no.',
        'Photographs of participants and event',
        'Remarks',
        'Source File'
    ]
    
    # Set headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Set column widths for professional appearance
    column_widths = [8, 12, 18, 15, 35, 25, 20, 15, 20]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
    
    # Add data with alternating row colors
    light_fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
    
    for i, record in enumerate(records, 1):
        row_data = [
            i,
            record.get('awc_no', ''),
            record.get('aww_name', ''),
            record.get('date', ''),
            record.get('event_name', ''),
            f"{record.get('participant_name', '')} - {record.get('phone', '')}".strip(' -'),
            '',  # Photographs column
            record.get('remarks', ''),
            record.get('source_file', '')
        ]
        
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=i+1, column=col, value=value)
            cell.font = data_font
            cell.alignment = data_alignment
            cell.border = border
            
            # Alternating row colors
            if i % 2 == 0:
                cell.fill = light_fill
    
    # Set row heights
    for row in range(1, len(records) + 2):
        ws.row_dimensions[row].height = 30
    
    return wb

def main():
    print("=== PROFESSIONAL CBE REPORT CONSOLIDATION ===")
    print("Excluding template and duplicate files...\n")
    
    source_files = get_source_excel_files()
    print(f"Processing {len(source_files)} source files:\n")
    
    for i, filename in enumerate(source_files, 1):
        print(f"  {i:2d}. {filename}")
    
    print("\n" + "="*60)
    
    all_records = []
    
    for filename in source_files:
        records = process_excel_file(filename)
        all_records.extend(records)
    
    print(f"\nTotal records extracted: {len(all_records)}")
    
    # Remove duplicates based on key fields
    unique_records = []
    seen_keys = set()
    
    for record in all_records:
        key = f"{record['awc_no']}-{record['aww_name']}-{record['participant_name']}-{record['phone']}"
        if key not in seen_keys and (record['aww_name'] or record['participant_name']):
            unique_records.append(record)
            seen_keys.add(key)
    
    print(f"Unique records after deduplication: {len(unique_records)}")
    
    if unique_records:
        wb = create_professional_excel(unique_records)
        output_file = f"Professional_CBE_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(output_file)
        
        print(f"\n✅ SUCCESS!")
        print(f"📊 Professional consolidated report saved: {output_file}")
        print(f"📈 Total unique records: {len(unique_records)}")
        print(f"📁 Source files processed: {len(source_files)}")
        
        # Create summary
        aww_count = len(set(r['aww_name'] for r in unique_records if r['aww_name']))
        awc_count = len(set(r['awc_no'] for r in unique_records if r['awc_no']))
        
        print(f"👥 Unique AWWs: {aww_count}")
        print(f"🏢 Unique AWCs: {awc_count}")
        
    else:
        print("❌ No records found to consolidate!")

if __name__ == "__main__":
    main()
