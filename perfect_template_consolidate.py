import pandas as pd
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
import os
import glob
import warnings
from datetime import datetime
import re

warnings.filterwarnings('ignore')

def get_source_files():
    """Get all source Excel files"""
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls")
    
    exclude_files = ["SARITA DAGAR CBE REPORT.xlsx"]
    exclude_patterns = [
        'consolidated_cbe_report', 'professional_cbe_report', 
        'comprehensive_cbe_report', 'final_complete_cbe_report',
        'exact_template_cbe_report', 'perfect_template_cbe_report'
    ]
    
    source_files = []
    for file in sorted(all_files):
        if file in exclude_files:
            continue
        
        file_lower = file.lower()
        should_skip = any(pattern in file_lower for pattern in exclude_patterns)
        
        if not should_skip:
            source_files.append(file)
    
    return source_files

def process_file_data(filename):
    """Process file and extract all meaningful data"""
    print(f"Processing: {filename}")
    records = []
    
    try:
        # Read file data
        if filename.endswith('.xls'):
            sheets_dict = pd.read_excel(filename, sheet_name=None, engine='xlrd')
        else:
            sheets_dict = pd.read_excel(filename, sheet_name=None)
        
        for sheet_name, df in sheets_dict.items():
            if df.empty:
                continue
                
            print(f"  Processing sheet: {sheet_name} ({df.shape[0]} rows)")
            
            # Process all rows to extract data
            for idx, row in df.iterrows():
                if idx > 1000:  # Reasonable limit
                    break
                
                # Convert to strings and clean
                row_data = []
                for cell in row:
                    if pd.notna(cell) and str(cell).strip() and str(cell).lower() != 'nan':
                        row_data.append(str(cell).strip())
                    else:
                        row_data.append('')
                
                # Skip empty rows
                if not any(row_data):
                    continue
                
                # Skip obvious header rows
                row_text = ' '.join(row_data).lower()
                if any(keyword in row_text for keyword in ['sno', 's.no', 'awc no', 'aww name', 'date of event']):
                    continue
                
                # Extract structured data
                record = extract_data_from_row(row_data, filename, sheet_name)
                if record and has_meaningful_data(record):
                    records.append(record)
        
        print(f"  Extracted {len(records)} records")
        return records
        
    except Exception as e:
        print(f"  Error: {e}")
        return []

def extract_data_from_row(row_data, filename, sheet_name):
    """Extract structured data from a single row"""
    record = {
        'source_file': filename,
        'sheet_name': sheet_name,
        'awc_no': '',
        'aww_name': '',
        'date': '',
        'event_name': '',
        'participant_name': '',
        'phone': '',
        'remarks': '',
        'raw_data': ' | '.join(row_data)
    }
    
    for i, cell in enumerate(row_data):
        if not cell:
            continue
        
        cell_clean = cell.strip()
        
        # AWC number (usually numeric, 1-3 digits)
        if cell_clean.isdigit() and 1 <= len(cell_clean) <= 3 and not record['awc_no']:
            record['awc_no'] = cell_clean
        
        # Date detection (various formats)
        elif re.search(r'\d{4}|\d{1,2}[./]\d{1,2}', cell_clean):
            if not record['date']:
                record['date'] = standardize_date(cell_clean)
        
        # Phone number and participant extraction
        elif re.search(r'[6-9]\d{9}', cell_clean):
            name, phone = extract_name_phone(cell_clean)
            if name and not record['participant_name']:
                record['participant_name'] = name
            if phone and not record['phone']:
                record['phone'] = phone
        
        # Event name detection
        elif any(event in cell_clean.lower() for event in [
            'handwash', 'hand wash', 'godbharai', 'god bharai', 'annaprashan', 
            'pre-school', 'preschool', 'breast feeding', 'breastfeeding'
        ]):
            if not record['event_name']:
                record['event_name'] = cell_clean
        
        # AWW name (alphabetic, reasonable length, usually early in row)
        elif (re.match(r'^[a-zA-Z\s]+$', cell_clean) and 3 <= len(cell_clean) <= 30 and 
              i < 6 and not record['aww_name']):
            record['aww_name'] = cell_clean
        
        # Participant name (fallback if no phone found)
        elif (re.match(r'^[a-zA-Z\s]+$', cell_clean) and 3 <= len(cell_clean) <= 30 and 
              not record['participant_name'] and record['aww_name']):
            record['participant_name'] = cell_clean
        
        # Remarks (usually later columns)
        elif i > 5 and cell_clean and not record['remarks'] and len(cell_clean) > 2:
            record['remarks'] = cell_clean
    
    return record

def has_meaningful_data(record):
    """Check if record has meaningful data"""
    return (record['aww_name'] or record['participant_name'] or 
            record['awc_no'] or record['event_name'] or record['phone'])

def extract_name_phone(text):
    """Extract name and phone from combined text"""
    if not text:
        return "", ""
    
    # Find phone number
    phone_match = re.search(r'[6-9]\d{9}', text)
    phone = phone_match.group() if phone_match else ""
    
    # Extract name by removing phone and cleaning
    name = text
    if phone:
        name = re.sub(r'[6-9]\d{9}', '', name)
    
    # Clean name
    name = re.sub(r'[-=,\s]+', ' ', name).strip()
    name = re.sub(r'^[^a-zA-Z]*', '', name)
    name = re.sub(r'[^a-zA-Z\s]*$', '', name)
    
    return name.strip(), phone

def standardize_date(date_val):
    """Standardize date format to DD-MM-YYYY"""
    if not date_val:
        return ""
    
    if isinstance(date_val, datetime):
        return date_val.strftime('%d-%m-%Y')
    
    date_str = str(date_val).strip()
    
    # Try different patterns and convert to DD-MM-YYYY
    patterns = [
        (r'(\d{4})-(\d{1,2})-(\d{1,2})', lambda m: f"{m.group(3):0>2}-{m.group(2):0>2}-{m.group(1)}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{4})', lambda m: f"{m.group(1):0>2}-{m.group(2):0>2}-{m.group(3)}"),
        (r'(\d{1,2})/(\d{1,2})/(\d{4})', lambda m: f"{m.group(1):0>2}-{m.group(2):0>2}-{m.group(3)}"),
        (r'(\d{1,2})\.(\d{1,2})\.(\d{2})', lambda m: f"{m.group(1):0>2}-{m.group(2):0>2}-20{m.group(3)}"),
    ]
    
    for pattern, formatter in patterns:
        match = re.search(pattern, date_str)
        if match:
            try:
                return formatter(match)
            except:
                continue
    
    return date_str

def create_perfect_template_excel(all_records):
    """Create Excel file with EXACT template structure as shown in image"""
    wb = Workbook()
    ws = wb.active
    ws.title = "CBE Report"
    
    # Define exact styles as per template
    header_font = Font(name='Arial', size=11, bold=True)
    data_font = Font(name='Arial', size=10)
    
    border_thin = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    alignment_center = Alignment(horizontal='center', vertical='center', wrap_text=True)
    alignment_left = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    # EXACT HEADER STRUCTURE AS SHOWN IN IMAGE
    
    # Row 1: Name of District
    ws.cell(row=1, column=1, value="Name of District:").font = header_font
    ws.cell(row=1, column=2, value="SOUTH WEST DELHI").font = data_font
    
    # Row 2: Name of Project  
    ws.cell(row=2, column=1, value="Name of Project:").font = header_font
    ws.cell(row=2, column=2, value="NAJAFGARH").font = data_font
    
    # Row 3: Month & Year
    ws.cell(row=3, column=1, value="Month & Year of activity held:").font = header_font
    ws.cell(row=3, column=2, value="MAY 2023 TO DECEMBER 2024").font = data_font
    
    # Empty row 4
    
    # Row 5: Table headers EXACTLY as shown in image
    current_row = 5
    
    headers = [
        "S.No.",
        "Number of AWC", 
        "Name of AWW",
        "Date of Event",
        "Name of Event along with name of reg. beneficiary concerned for the event",
        "Names of participants with Phone Number",
        "Photographs of participants and event",
        "Remarks"
    ]
    
    # Set headers with exact formatting
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=current_row, column=col, value=header)
        cell.font = header_font
        cell.alignment = alignment_center
        cell.border = border_thin
    
    # Set column widths to match template exactly
    column_widths = [8, 15, 20, 15, 45, 30, 25, 20]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(col)].width = width
    
    # Set header row height
    ws.row_dimensions[current_row].height = 50
    
    current_row += 1
    
    # Add data rows
    for i, record in enumerate(all_records, 1):
        # Combine participant name and phone exactly as template shows
        participant_info = record.get('participant_name', '')
        if record.get('phone'):
            if participant_info:
                participant_info += f" - {record['phone']}"
            else:
                participant_info = record['phone']
        
        row_data = [
            i,  # S.No.
            record.get('awc_no', ''),  # Number of AWC
            record.get('aww_name', ''),  # Name of AWW
            record.get('date', ''),  # Date of Event
            record.get('event_name', ''),  # Name of Event along with...
            participant_info,  # Names of participants with Phone Number
            '',  # Photographs of participants and event (placeholder)
            record.get('remarks', '')  # Remarks
        ]
        
        # Add data to cells with exact formatting
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=current_row, column=col, value=value)
            cell.font = data_font
            cell.alignment = alignment_left
            cell.border = border_thin
        
        # Set row height to accommodate content
        ws.row_dimensions[current_row].height = 40
        current_row += 1
    
    return wb

def main():
    print("=== PERFECT TEMPLATE CBE REPORT CONSOLIDATION ===")
    print("Creating Excel with EXACT structure as shown in your image...\n")
    
    source_files = get_source_files()
    print(f"Processing {len(source_files)} files:\n")
    
    for i, filename in enumerate(source_files, 1):
        print(f"  {i:2d}. {filename}")
    
    print("\n" + "="*80)
    
    all_records = []
    
    for filename in source_files:
        records = process_file_data(filename)
        all_records.extend(records)
    
    # Remove duplicates based on key fields
    unique_records = []
    seen_keys = set()
    
    for record in all_records:
        key = f"{record['awc_no']}-{record['aww_name']}-{record['participant_name']}-{record['phone']}"
        if key not in seen_keys:
            unique_records.append(record)
            seen_keys.add(key)
    
    print(f"\n📊 CONSOLIDATION SUMMARY:")
    print(f"   Files processed: {len(source_files)}")
    print(f"   Total records extracted: {len(all_records)}")
    print(f"   Unique records: {len(unique_records)}")
    
    if unique_records:
        print(f"\n🔄 Creating Excel with PERFECT template structure...")
        wb = create_perfect_template_excel(unique_records)
        
        output_file = f"Perfect_Template_CBE_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        wb.save(output_file)
        
        print(f"\n✅ PERFECT SUCCESS!")
        print(f"📁 Report saved: {output_file}")
        print(f"📊 Records included: {len(unique_records)}")
        print(f"📋 EXACT template structure maintained")
        print(f"🎯 Professional formatting applied")
        print(f"📝 Ready for image insertion")
        
        # Statistics
        unique_awws = len(set(r['aww_name'] for r in unique_records if r['aww_name']))
        unique_awcs = len(set(r['awc_no'] for r in unique_records if r['awc_no']))
        
        print(f"\n📈 DATA STATISTICS:")
        print(f"   Unique AWWs: {unique_awws}")
        print(f"   Unique AWCs: {unique_awcs}")
        print(f"   Files with data: {len(source_files)}")
        
        # Show sample data
        print(f"\n📋 SAMPLE DATA (first 3 records):")
        for i, record in enumerate(unique_records[:3], 1):
            print(f"   {i}. AWC: {record['awc_no']}, AWW: {record['aww_name']}, Event: {record['event_name']}")
        
    else:
        print("❌ No records found!")

if __name__ == "__main__":
    main()
